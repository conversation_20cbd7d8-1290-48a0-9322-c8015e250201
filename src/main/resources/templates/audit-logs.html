<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>操作日志</title>
    <!-- 页面特定CSS -->
    <th:block th:fragment="pageCss">
        <style>
            /* 卡片样式 */
            .card {
                border-radius: 0.5rem;
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                margin-bottom: 1.5rem;
            }
            
            .card-header {
                background-color: rgba(0, 0, 0, 0.03);
                border-bottom: 1px solid rgba(0, 0, 0, 0.125);
                padding: 0.75rem 1.25rem;
            }
            
            /* 表格样式 */
            .table th {
                font-weight: 500;
                background-color: #f8f9fa;
            }
            
            .table-hover tbody tr:hover {
                background-color: rgba(0, 123, 255, 0.05);
            }
            
            /* 徽章样式 */
            .badge {
                font-weight: 500;
                padding: 0.4em 0.6em;
                border-radius: 0.25rem;
            }
            
            /* 过滤器样式 */
            .filter-card {
                border-left: 4px solid #007bff;
            }
            
            /* 分页样式 */
            .pagination {
                margin-bottom: 0;
            }
            
            /* 加载动画样式 */
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }
            
            /* 自动刷新切换开关 */
            .form-switch .form-check-input {
                width: 2.5em;
                margin-left: -2.8em;
            }
            
            /* 详情模态框样式 */
            .detail-label {
                font-weight: 500;
                color: #6c757d;
            }
            
            .detail-value {
                font-weight: 400;
            }
            
            .detail-row {
                margin-bottom: 0.75rem;
                padding-bottom: 0.75rem;
                border-bottom: 1px solid #e9ecef;
            }
            
            .detail-row:last-child {
                border-bottom: none;
            }
        </style>
    </th:block>
</head>
<body>

    <!-- 使用默认布局 -->
    <th:block th:replace="~{layout/default :: html(
        content=~{::content},
        pageTitle='操作记录',
        pageCss=~{::pageCss},
        pageScript=~{::pageScript}
    )}">

    <th:block th:fragment="content">
        <div class="container-fluid py-3">
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">操作日志</h5>
                        </div>
                        <div class="card-body">
                            <!-- 过滤条件 -->
                            <form id="filter-form" class="row g-3 mb-4">
                                <div class="col-md-2">
                                    <label for="username" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="username" placeholder="输入用户名">
                                </div>
                                <div class="col-md-2">
                                    <label for="operation-type" class="form-label">操作类型</label>
                                    <select class="form-select" id="operation-type">
                                        <option value="">全部</option>
                                        <option value="LOGIN">登录</option>
                                        <option value="LOGOUT">登出</option>
                                        <option value="CREATE">创建</option>
                                        <option value="UPDATE">更新</option>
                                        <option value="DELETE">删除</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="target-entity" class="form-label">目标实体</label>
                                    <select class="form-select" id="target-entity">
                                        <option value="">全部</option>
                                        <option value="User">用户</option>
                                        <option value="Reservation">预约</option>
                                        <option value="MeetingRoom">会议室</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status">
                                        <option value="">全部</option>
                                        <option value="SUCCESS">成功</option>
                                        <option value="FAILURE">失败</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="start-date" class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="start-date">
                                </div>
                                <div class="col-md-2">
                                    <label for="end-date" class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="end-date">
                                </div>
                                <div class="col-12 text-end">
                                    <button type="button" id="reset-filter" class="btn btn-outline-secondary me-2">
                                        <i class="bi bi-x-circle"></i> 重置
                                    </button>
                                    <button type="button" id="apply-filter" class="btn btn-primary">
                                        <i class="bi bi-funnel"></i> 筛选
                                    </button>
                                </div>
                            </form>

                            <!-- 日志表格 -->
                            <div class="table-responsive position-relative">
                                <table class="table table-striped table-hover align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th scope="col">#</th>
                                            <th scope="col">用户</th>
                                            <th scope="col">操作类型</th>
                                            <th scope="col">目标实体</th>
                                            <th scope="col">状态</th>
                                            <th scope="col">时间</th>
                                            <th scope="col">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="logs-table-body">
                                        <!-- 日志数据将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页和统计信息 -->
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="text-muted">
                                    显示第 <span id="start-index">0</span> 到 <span id="end-index">0</span> 条，共 <span id="total-count">0</span> 条记录
                                </div>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination" id="pagination">
                                        <!-- 分页将通过JavaScript动态加载 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 日志详情模态框 -->
        <div class="modal fade" id="log-detail-modal" tabindex="-1" aria-labelledby="log-detail-modal-label" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="log-detail-modal-label">操作日志详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="container-fluid">
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">ID</div>
                                <div class="col-md-9 detail-value" id="detail-id">-</div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">用户</div>
                                <div class="col-md-9 detail-value" id="detail-username">-</div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">操作类型</div>
                                <div class="col-md-9 detail-value" id="detail-operation-type">-</div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">目标实体</div>
                                <div class="col-md-9 detail-value" id="detail-target-entity">-</div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">目标ID</div>
                                <div class="col-md-9 detail-value" id="detail-target-id">-</div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">状态</div>
                                <div class="col-md-9 detail-value" id="detail-status">-</div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">IP地址</div>
                                <div class="col-md-9 detail-value" id="detail-ip">-</div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-md-3 detail-label">时间</div>
                                <div class="col-md-9 detail-value" id="detail-time">-</div>
                            </div>
                            <div class="row">
                                <div class="col-md-3 detail-label">详细信息</div>
                                <div class="col-md-9 detail-value">
                                    <pre id="detail-details" class="p-2 bg-light rounded">-</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </th:block>

     </th:block>
    
    <th:block th:fragment="pageScript">
        <script th:inline="javascript">
            $(document).ready(function() {
                // 初始化变量
                let currentPage = 0;
                let pageSize = 10;
                let autoRefreshInterval;
                
                // 初始化页面
                loadAuditLogs();
                setupEventListeners();
                
                // 设置自动刷新切换
                $('#auto-refresh').change(function() {
                    if($(this).is(':checked')) {
                        autoRefreshInterval = setInterval(function() {
                            loadAuditLogs();
                        }, 30000); // 每30秒刷新一次
                    } else {
                        clearInterval(autoRefreshInterval);
                    }
                });
                
                // 加载操作日志
                function loadAuditLogs() {
                    const filters = getFilters();
                    
                    // 显示加载中状态
                    $('#logs-table-body').html(`
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载日志数据...</p>
                            </td>
                        </tr>
                    `);
                    
                    // 发送AJAX请求
                    $.ajax({
                        url: (window.contextPath ? window.contextPath.buildApiUrl('/audit-logs/data') : '/audit-logs/data'),
                        method: 'GET',
                        data: {
                            ...filters,
                            page: currentPage,
                            size: pageSize
                        },
                        success: function(response) {
                            updateTable(response);
                            updatePagination(response);
                        },
                        error: function(xhr, status, error) {
                            $('#logs-table-body').html(`
                                <tr>
                                    <td colspan="7" class="text-center text-danger py-4">
                                        <i class="bi bi-exclamation-triangle-fill fs-4"></i>
                                        <p class="mt-2">加载日志失败: ${error}</p>
                                    </td>
                                </tr>
                            `);
                        }
                    });
                }
                
                // 获取过滤条件
                function getFilters() {
                    return {
                        username: $('#username').val(),
                        operationType: $('#operation-type').val(),
                        targetEntity: $('#target-entity').val(),
                        status: $('#status').val(),
                        startDate: $('#start-date').val(),
                        endDate: $('#end-date').val()
                    };
                }
                
                // 更新表格
                function updateTable(response) {
                    const logs = response.content;
                    let html = '';
                    
                    if (logs && logs.length > 0) {
                        logs.forEach(function(log) {
                            // 根据状态设置徽章样式
                            let statusBadgeClass = 'bg-secondary';
                            if (log.status === 'SUCCESS') {
                                statusBadgeClass = 'bg-success';
                            } else if (log.status === 'FAILURE') {
                                statusBadgeClass = 'bg-danger';
                            }
                            
                            // 根据操作类型设置徽章样式
                            let operationBadgeClass = 'bg-primary';
                            if (log.operationType === 'CREATE') {
                                operationBadgeClass = 'bg-success';
                            } else if (log.operationType === 'UPDATE') {
                                operationBadgeClass = 'bg-info';
                            } else if (log.operationType === 'DELETE') {
                                operationBadgeClass = 'bg-danger';
                            } else if (log.operationType === 'LOGIN') {
                                operationBadgeClass = 'bg-primary';
                            } else if (log.operationType === 'LOGOUT') {
                                operationBadgeClass = 'bg-secondary';
                            }
                            
                            html += `
                                <tr class="audit-log-item">
                                    <td>${log.id}</td>
                                    <td>${log.username || '-'}</td>
                                    <td><span class="badge ${operationBadgeClass}">${log.operationType || '未知'}</span></td>
                                    <td>${log.targetEntity || '-'}</td>
                                    <td><span class="badge ${statusBadgeClass}">${log.status || '未知'}</span></td>
                                    <td>${log.createdTime || '-'}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary view-log-btn" data-log-id="${log.id}">
                                            <i class="bi bi-eye"></i> 详情
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });
                    } else {
                        html = `
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="bi bi-inbox fs-4 text-muted"></i>
                                    <p class="mt-2 text-muted">没有找到符合条件的日志记录</p>
                                </td>
                            </tr>
                        `;
                    }
                    
                    $('#logs-table-body').html(html);
                    
                    // 绑定查看详情按钮事件
                    $('.view-log-btn').on('click', function() {
                        const logId = $(this).data('log-id');
                        viewLogDetail(logId);
                    });
                    
                    // 更新分页信息
                    const startIndex = response.totalElements > 0 ? (response.number * response.size + 1) : 0;
                    const endIndex = response.totalElements > 0 ? Math.min(startIndex + response.numberOfElements - 1, response.totalElements) : 0;
                    
                    $('#start-index').text(startIndex);
                    $('#end-index').text(endIndex);
                    $('#total-count').text(response.totalElements);
                }
                
                // 更新分页
                function updatePagination(response) {
                    const totalPages = response.totalPages;
                    let html = '';
                    
                    // 上一页按钮
                    html += `
                        <li class="page-item ${response.first ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${response.number - 1}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    `;
                    
                    // 页码按钮
                    const startPage = Math.max(0, response.number - 2);
                    const endPage = Math.min(totalPages - 1, response.number + 2);
                    
                    // 如果当前页不是第一页，显示第一页
                    if (startPage > 0) {
                        html += `
                            <li class="page-item">
                                <a class="page-link" href="#" data-page="0">1</a>
                            </li>
                        `;
                        
                        // 如果当前页与第一页之间有间隔，显示省略号
                        if (startPage > 1) {
                            html += `
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                            `;
                        }
                    }
                    
                    // 显示当前页附近的页码
                    for (let i = startPage; i <= endPage; i++) {
                        html += `
                            <li class="page-item ${i === response.number ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i + 1}</a>
                            </li>
                        `;
                    }
                    
                    // 如果当前页不是最后一页，显示最后一页
                    if (endPage < totalPages - 1) {
                        // 如果当前页与最后一页之间有间隔，显示省略号
                        if (endPage < totalPages - 2) {
                            html += `
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                            `;
                        }
                        
                        html += `
                            <li class="page-item">
                                <a class="page-link" href="#" data-page="${totalPages - 1}">${totalPages}</a>
                            </li>
                        `;
                    }
                    
                    // 下一页按钮
                    html += `
                        <li class="page-item ${response.last ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${response.number + 1}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    `;
                    
                    $('#pagination').html(html);
                    
                    // 绑定页码点击事件
                    $('.page-link[data-page]').on('click', function(e) {
                        e.preventDefault();
                        const page = $(this).data('page');
                        
                        if (page !== currentPage && !$(this).parent().hasClass('disabled')) {
                            currentPage = page;
                            loadAuditLogs();
                        }
                    });
                }
                
                // 查看日志详情
                function viewLogDetail(logId) {
                    $.ajax({
                        url: `/audit-logs/${logId}`,
                        method: 'GET',
                        success: function(log) {

                            // 填充模态框数据
                            $('#detail-id').text(log.id || '-');
                            $('#detail-time').text(log.createdTime || '-');
                            $('#detail-username').text(log.username || '-');
                            $('#detail-operation-type').text(log.operationType || '-');
                            $('#detail-status').text(log.status || '-');
                            $('#detail-target-entity').text(log.targetEntity || '-');
                            $('#detail-target-id').text(log.targetId || '-');
                            $('#detail-ip').text(log.ipAddress || '-');
                            $('#detail-details').text(log.details || '无详细信息');
                            
                            // 根据状态设置徽章样式
                            let statusBadgeClass = 'bg-secondary';
                            if (log.status === 'SUCCESS') {
                                statusBadgeClass = 'bg-success';
                            } else if (log.status === 'FAILURE') {
                                statusBadgeClass = 'bg-danger';
                            }
                            
                            // 根据操作类型设置徽章样式
                            let operationBadgeClass = 'bg-primary';
                            if (log.operationType === 'CREATE') {
                                operationBadgeClass = 'bg-success';
                            } else if (log.operationType === 'UPDATE') {
                                operationBadgeClass = 'bg-info';
                            } else if (log.operationType === 'DELETE') {
                                operationBadgeClass = 'bg-danger';
                            } else if (log.operationType === 'LOGIN') {
                                operationBadgeClass = 'bg-primary';
                            } else if (log.operationType === 'LOGOUT') {
                                operationBadgeClass = 'bg-secondary';
                            }
                            
                            // 使用徽章显示状态和操作类型
                            $('#detail-status').html(`<span class="badge ${statusBadgeClass}">${log.status || '未知'}</span>`);
                            $('#detail-operation-type').html(`<span class="badge ${operationBadgeClass}">${log.operationType || '未知'}</span>`);
                            
                            // 显示模态框
                            const modal = new bootstrap.Modal(document.getElementById('log-detail-modal'));
                            modal.show();
                        },
                        error: function(xhr, status, error) {
                            alert('加载日志详情失败: ' + error);
                        }
                    });
                }
                
                // 设置事件监听器
                function setupEventListeners() {
                    // 应用过滤按钮点击事件
                    $('#apply-filter').on('click', function() {
                        currentPage = 0; // 重置为第一页
                        loadAuditLogs();
                    });
                    
                    // 重置过滤按钮点击事件
                    $('#reset-filter').on('click', function() {
                        $('#filter-form')[0].reset();
                        currentPage = 0; // 重置为第一页
                        loadAuditLogs();
                    });
                    
                    // 刷新按钮点击事件
                    $('#refresh-logs').on('click', function() {
                        loadAuditLogs();
                    });

                }

            });
        </script>
    </th:block>


</body>
</html>
