<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" 
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle != null ? pageTitle + ' - 会议室预约系统' : '会议室预约系统'}">会议室预约系统</title>

    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- FullCalendar CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css">

    <!-- FullCalendar Resource Plugins - 确保 resource-common 在其他资源插件之前加载 -->
    <link rel="stylesheet" href='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-common@6.1.8/index.global.min.css'>
    <link rel="stylesheet" href='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-daygrid@6.1.8/index.global.min.css'>
    <link rel="stylesheet" href='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-timegrid@6.1.8/index.global.min.css'>
    <link rel="stylesheet" href='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-timeline@6.1.8/index.global.min.css'>

    <!-- 自定义CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <!-- 页面特定CSS -->
    <th:block th:replace="${pageCss} ?: ~{}"></th:block>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">会议室预约系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/dashboard}" th:classappend="${#strings.equals(#vars.pageTitle, '仪表盘') ? 'active' : ''}">仪表盘</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/reservations/my}" th:classappend="${#strings.equals(#vars.pageTitle, '我的预约') ? 'active' : ''}">我的预约</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/reservations/create}" th:classappend="${#strings.equals(#vars.pageTitle, '预约会议室') ? 'active' : ''}">预约会议室</a>
                    </li>
                    <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                        <a class="nav-link" th:href="@{/meeting-rooms}" th:classappend="${#strings.equals(#vars.pageTitle, '会议室管理') ? 'active' : ''}">会议室管理</a>
                    </li>
                    <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                        <a class="nav-link" th:href="@{/admin/users}" th:classappend="${#strings.equals(#vars.pageTitle, '用户管理') ? 'active' : ''}">用户管理</a>
                    </li>
                    <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                        <a class="nav-link" th:href="@{/audit-logs}" th:classappend="${#strings.equals(#vars.pageTitle, '操作日志') ? 'active' : ''}">操作日志</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/about}" th:classappend="${#strings.equals(#vars.pageTitle, '关于系统') ? 'active' : ''}">关于系统</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown" sec:authorize="isAuthenticated()">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <span sec:authentication="name">用户名</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" th:href="@{/profile}">个人资料</a></li>
                            <li><a class="dropdown-item" th:href="@{/profile/change-password}">修改密码</a></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="dropdown-item p-0">
                                    <button type="submit" class="btn btn-link text-decoration-none w-100 text-start px-3 py-1">退出登录</button>
                                </form>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/login}">登录</a>
                    </li>
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/register}">注册</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="container mt-4">
        <!-- 消息提示 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <span th:text="${successMessage}">操作成功</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <span th:text="${errorMessage}">操作失败</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- 页面内容 -->
        <th:block th:replace="${content} ?: ~{}"></th:block>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0"> 会议室预约系统 - 企业内部使用</p>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- FullCalendar JS -->
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
    
    <!-- FullCalendar Resource Plugins -->
    <script src='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-common@6.1.8/index.global.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-daygrid@6.1.8/index.global.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-timegrid@6.1.8/index.global.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/@fullcalendar/resource-timeline@6.1.8/index.global.min.js'></script>
    
    <!-- Context Path工具 -->
    <script th:src="@{/js/context-path.js}"></script>
    <!-- 自定义JS -->
    <script th:src="@{/js/main.js}"></script>

    <!-- 页面特定JS -->
    <th:block th:replace="${pageScript} ?: ~{}"></th:block>
</body>
</html>
