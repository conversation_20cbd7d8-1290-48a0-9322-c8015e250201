<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘 - 会议室预约系统</title>

    <!-- 页面特定CSS -->
    <th:block th:fragment="pageCss">
        <style>
            body {
                overflow: hidden;
            }
            
            #calendar {
                height: calc(100vh - 200px);
                min-height: 500px;
                overflow: visible;
                background-color: #fff;
            }
            
            /* 确保日历有滚动条 */
            .fc-scroller {
                overflow: auto !important;
                max-height: calc(100vh - 250px) !important;
            }
            
            .fc-time-grid-container {
                overflow-y: auto !important;
                height: auto !important;
                min-height: 500px !important;
            }
            
            .fc-view-harness {
                height: auto !important;
                min-height: 500px !important;
            }
            
            .fc-event {
                cursor: pointer;
                border-radius: 4px;
                padding: 2px 4px;
                margin-bottom: 2px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.12);
                transition: all 0.2s ease;
            }
            .fc-event:hover {
                box-shadow: 0 3px 6px rgba(0,0,0,0.16);
                transform: translateY(-1px);
            }
            .room-filter-item {
                cursor: pointer;
                transition: background-color 0.2s ease;
                border-left: 3px solid transparent;
            }
            .room-filter-item:hover {
                background-color: #f8f9fa;
            }
            .room-filter-item.active {
                background-color: #0d6efd !important;
                color: #fff !important;
            }
            .room-filter-item.active span {
                color: #fff !important;
            }
            .dashboard-card {
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                transition: all 0.3s ease;
                height: 100%;
            }
            .dashboard-card .card-header {
                border-bottom: 1px solid rgba(0,0,0,0.05);
                background-color: rgba(0,0,0,0.02);
                font-weight: 600;
            }
            .calendar-container {
                height: calc(100vh - 100px);
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            .calendar-card {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            .calendar-card .card-body {
                flex: 1;
                padding: 0.75rem;
                overflow: hidden;
            }
            
            /* 控制面板高度控制 */
            .control-panel {
                height: calc(100vh - 100px);
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }
            
            .control-panel .card-body {
                flex: 1;
                overflow-y: auto;
                scrollbar-width: thin;
            }
            
            /* 自定义滚动条样式 */
            .control-panel .card-body::-webkit-scrollbar {
                width: 6px;
            }
            
            .control-panel .card-body::-webkit-scrollbar-track {
                background: #f1f1f1;
            }
            
            .control-panel .card-body::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }
            
            /* 会议室列表控制 */
            #room-filter {
                max-height: 250px;
                overflow-y: auto;
                scrollbar-width: thin;
            }
            
            #room-filter::-webkit-scrollbar {
                width: 6px;
            }
            
            #room-filter::-webkit-scrollbar-track {
                background: #f1f1f1;
            }
            
            #room-filter::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }
            
            .btn-group .btn {
                box-shadow: none !important;
            }
            .btn-outline-primary:hover {
                background-color: rgba(13, 110, 253, 0.1);
                color: #0d6efd;
            }
            .btn-outline-primary.active {
                background-color: #0d6efd;
                color: white;
            }
            
            /* 响应式调整 */
            @media (max-width: 1199px) {
                .calendar-container {
                    height: calc(100vh - 120px);
                }
                .control-panel {
                    height: auto;
                    max-height: none;
                }
            }

            @media (max-width: 768px) {
                body {
                    overflow: auto;
                }
                .calendar-container {
                    height: 500px;
                }
                #calendar {
                    height: 100%;
                }

                /* 移动端优化 */
                .control-panel {
                    margin-bottom: 1rem;
                }

                .btn-group .btn {
                    font-size: 0.875rem;
                    padding: 0.375rem 0.5rem;
                }

                .card-body {
                    padding: 0.75rem;
                }

                .fc-toolbar-title {
                    font-size: 1rem !important;
                }

                .fc-button {
                    padding: 0.25rem 0.5rem !important;
                    font-size: 0.75rem !important;
                }
            }

            @media (max-width: 576px) {
                .container {
                    padding-left: 0.5rem;
                    padding-right: 0.5rem;
                }

                .calendar-container {
                    height: 400px;
                }

                .btn-group .btn {
                    font-size: 0.75rem;
                    padding: 0.25rem 0.375rem;
                }

                .fc-timegrid-slot, .fc-timegrid-axis {
                    height: 25px !important;
                }
            }
            
            /* 日历样式优化 */
            .fc-view {
                background-color: #fff;
            }
            
            .fc-col-header {
                background-color: #f8f9fa;
            }
            
            .fc-col-header-cell {
                padding: 6px 0 !important;
            }
            
            .fc-toolbar-title {
                font-size: 1.2rem !important;
                font-weight: 500 !important;
                padding: 0.5rem 1rem;
                background-color: #f8f9fa;
                border-radius: 4px;
                margin-bottom: 0.5rem !important;
            }

            .fc .fc-toolbar.fc-header-toolbar {
                margin-bottom: 0.75rem;
            }

            .fc-header-toolbar .fc-toolbar-chunk:nth-child(2) {
                flex-grow: 0;
            }

            .fc-day-today {
                background-color: rgba(13, 110, 253, 0.05) !important;
            }

            .fc-timegrid-slot, .fc-timegrid-axis {
                height: 35px !important;
            }
        </style>
    </th:block>
</head>
<body>
    <!-- 使用默认布局 -->
    <th:block th:replace="~{layout/default :: html(
        content=~{::content},
        pageTitle='仪表盘',
        pageCss=~{::pageCss},
        pageScript=~{::pageScript}
    )}">
        <th:block th:fragment="content">
            <div class="row">
                <!-- 左侧过滤器和快捷操作 -->
                <div class="col-md-3 mb-4">
                    <!-- 整合所有操作到一个卡片中 -->
                    <div class="card dashboard-card control-panel">
                        <div class="card-header">
                            <h5 class="mb-0">预约控制面板</h5>
                        </div>
                        <div class="card-body p-0">
                            <!-- 日期选择器 -->
                            <div class="p-3 border-bottom">
                                <h6 class="mb-2">日期选择</h6>
                                <div class="input-group mb-2">
                                    <label for="date-picker"></label><input type="date" id="date-picker" class="form-control">
                                    <button id="go-to-date" class="btn btn-primary">前往</button>
                                </div>
                            </div>

                            <!-- 视图选择 -->
                            <div class="p-3 border-bottom">
                                <h6 class="mb-2">视图选择</h6>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary" data-view="timeGridDay">日</button>
                                    <button type="button" class="btn btn-outline-primary" data-view="timeGridWeek">周</button>
                                    <button type="button" class="btn btn-outline-primary" data-view="dayGridMonth">月</button>
                                </div>
                            </div>

                            <!-- 会议室过滤器 -->
                            <div class="p-3 border-bottom">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">会议室</h6>
                                </div>
                                <ul id="room-filter" class="list-group list-group-flush">
                                    <!-- 会议室列表将通过JavaScript动态加载 -->
                                    <li class="list-group-item room-filter-item active" data-room-id="all">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>所有会议室</span>
                                            <span class="badge bg-primary rounded-pill">0</span>
                                        </div>
                                    </li>
                                    <!-- 通过API动态加载会议室列表 -->
                                </ul>
                            </div>

                            <!-- 快捷操作 -->
                            <div class="p-3 border-bottom">
                                <h6 class="mb-2">快捷操作</h6>
                                <div class="d-grid gap-2">
                                    <a th:href="@{/reservations/create}" class="btn btn-success">
                                        <i class="bi bi-plus-circle"></i> 新建预约
                                    </a>
                                    <a th:href="@{/reservations/my}" class="btn btn-primary">
                                        <i class="bi bi-list-check"></i> 我的预约
                                    </a>
                                </div>
                            </div>
                            
                            <!-- 最近操作记录 -->
                            <div class="p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">最近操作</h6>
                                    <a href="/audit-logs" class="btn btn-sm btn-outline-primary">
                                        更多
                                    </a>
                                </div>
                                <ul id="recent-audit-logs" class="list-group list-group-flush">
                                    <li class="list-group-item text-center">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <span class="ms-2">加载中...</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧日历视图 -->
                <div class="col-md-9">
                    <div class="calendar-container">
                        <div class="calendar-card">
                            <div class="card-body">
                                <!-- FullCalendar将在这里渲染 -->
                                <div id="calendar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 引入预约表单模态框 -->
            <!--<div th:replace="~{fragments/reservation-form-modal :: reservationFormModal}"></div>-->
            
            <!-- 预约详情模态框 -->
            <div class="modal fade" id="reservation-detail-modal" tabindex="-1" aria-labelledby="reservation-detail-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="reservation-detail-modal-label">预约详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="reservation-details-container">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <h4 id="reservation-title" class="text-primary"></h4>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <p><strong>会议室：</strong> <span id="reservation-room"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>状态：</strong> <span id="reservation-status"></span></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <p><strong>开始时间：</strong> <span id="reservation-start-time"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>结束时间：</strong> <span id="reservation-end-time"></span></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <p><strong>申请人：</strong> <span id="reservation-applicant"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>联系方式：</strong> <span id="reservation-contact"></span></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <p><strong>参会人数：</strong> <span id="reservation-attendees"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>创建者：</strong> <span id="reservation-creator"></span></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <p><strong>备注：</strong></p>
                                        <div id="reservation-notes" class="p-2 bg-light rounded"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </th:block>
    </th:block>

    <!-- 页面脚本 -->
    <script th:inline="javascript">
        let calendar;
        
        // 格式化日期时间为输入框格式
        function formatDateTimeForInput(date) {
            if (!date) return null;
            
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }
        
        // 处理日历点击事件 - 跳转到预约创建页面
        function handleCalendarClick(info) {
            // 获取点击的日期时间
            const clickedDate = info.start || info.date || info.dateStr;
            let url = '/reservations/create';
            
            // 添加日期时间参数
            if (clickedDate) {
                const startDate = clickedDate.toISOString().split('T')[0];
                const startTime = clickedDate.toTimeString().substring(0, 5);
                
                // 设置结束时间为开始时间后1小时
                const endDate = new Date(clickedDate.getTime() + 60 * 60 * 1000);
                const endTimeStr = endDate.toTimeString().substring(0, 5);
                
                url += `?date=${startDate}&startTime=${startTime}&endTime=${endTimeStr}`;
            }
            
            // 如果是资源视图，并且有选择的资源，则预设会议室
            if (info.resource) {
                url += `${url.includes('?') ? '&' : '?'}roomId=${info.resource.id}`;
            }
            
            // 跳转到预约创建页面
            window.location.href = url;
        }
        
        // 初始化日历
        function initCalendar() {
            const calendarEl = document.getElementById('calendar');
            
            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                headerToolbar: {
                    left: '',
                    center: '',
                    right: ''
                },
                locale: 'zh-cn',
                allDaySlot: false,
                slotMinTime: '08:00:00',
                slotMaxTime: '23:00:00',
                height: 'auto',
                expandRows: true,
                slotLabelFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                nowIndicator: true,
                navLinks: true,
                editable: false,
                selectable: true,
                selectMirror: true,
                dayMaxEvents: true,
                scrollTime: '09:00:00', // 默认滚动到上午9点
                eventSources: [
                    {
                        url: '/reservations/events',
                        method: 'GET',
                        failure: function() {
                            alert('加载预约数据失败');
                        }
                    }
                ],
                select: handleCalendarClick,
                eventClick: function(info) {
                    // 显示预约详情
                    const event = info.event;
                    const id = event.id;
                    
                    // 获取预约详情并显示在模态框中
                    $.ajax({
                        url: `/meeting/reservations/${id}/details`,
                        method: 'GET',
                        success: function(response) {
                            if (response.success) {
                                const reservation = response.reservation;
                                
                                // 填充模态框数据
                                $('#reservation-title').text(reservation.title);
                                $('#reservation-room').text(reservation.roomName);
                                
                                // 格式化状态显示
                                let statusText = '';
                                let statusClass = '';
                                if (reservation.status === 'confirmed') {
                                    statusText = '已确认';
                                    statusClass = 'text-success';
                                } else if (reservation.status === 'pending') {
                                    statusText = '待审批';
                                    statusClass = 'text-warning';
                                } else if (reservation.status === 'cancelled') {
                                    statusText = '已取消';
                                    statusClass = 'text-secondary';
                                }
                                $('#reservation-status').text(statusText).removeClass().addClass(statusClass);
                                
                                // 格式化时间显示
                                const startTime = new Date(reservation.startTime);
                                const endTime = new Date(reservation.endTime);
                                const dateTimeFormat = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' };
                                $('#reservation-start-time').text(startTime.toLocaleString('zh-CN', dateTimeFormat));
                                $('#reservation-end-time').text(endTime.toLocaleString('zh-CN', dateTimeFormat));
                                
                                // 填充其他信息
                                $('#reservation-applicant').text(reservation.applicantName || '未提供');
                                $('#reservation-contact').text(reservation.applicantContact || '未提供');
                                $('#reservation-attendees').text(reservation.attendeesCount || '未提供');
                                $('#reservation-creator').text(reservation.creatorName || '未知');
                                $('#reservation-notes').text(reservation.notes || '无备注');
                                
                                // 显示模态框
                                const modal = new bootstrap.Modal(document.getElementById('reservation-detail-modal'));
                                modal.show();
                            } else {
                                toastr.error(response.message || '获取预约详情失败');
                            }
                        },
                        error: function() {
                            toastr.error('获取预约详情失败，请稍后再试');
                        }
                    });
                },
                loading: function(isLoading) {
                    if (isLoading) {
                        $('#calendar-loading').show();
                    } else {
                        $('#calendar-loading').hide();
                    }
                }
            });
            
            calendar.render();
        }
        
        // 加载最近的审计日志
        function loadRecentAuditLogs() {
            $.ajax({
                url: '/meeting/audit-logs/recent',
                method: 'GET',
                data: {
                    limit: 3
                },
                success: function(response) {
                    updateRecentAuditLogs(response);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading recent audit logs:', error);
                    const recentLogsElement = document.getElementById('recent-audit-logs');
                    recentLogsElement.innerHTML = `
                        <li class="list-group-item text-center text-danger">
                            <i class="bi bi-exclamation-triangle"></i> 加载失败，请刷新重试
                        </li>
                    `;
                }
            });
        }
        
        // 更新最近审计日志显示
        function updateRecentAuditLogs(logs) {
            const recentLogsElement = document.getElementById('recent-audit-logs');
            
            if (!logs || logs.length === 0) {
                recentLogsElement.innerHTML = `
                    <li class="list-group-item text-center text-muted">
                        <i class="bi bi-info-circle"></i> 暂无操作记录
                    </li>
                `;
                return;
            }
            
            let html = '';
            
            logs.forEach(function(log) {
                // 格式化时间
                let formattedTime = '';
                try {
                    const timestamp = new Date(log.createdTime);
                    if (!isNaN(timestamp.getTime())) {
                        formattedTime = timestamp.toLocaleString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        });
                    }
                } catch (e) {
                    console.error('Date parsing error:', e);
                    formattedTime = ''; // 如果日期无效，不显示时间
                }
                
                // 状态徽章样式
                let statusBadgeClass = 'bg-secondary';
                if (log.status === 'SUCCESS') {
                    statusBadgeClass = 'bg-success';
                } else if (log.status === 'FAILURE') {
                    statusBadgeClass = 'bg-danger';
                } else if (log.status === 'WARNING') {
                    statusBadgeClass = 'bg-warning';
                }
                
                // 操作类型徽章样式
                let operationBadgeClass = 'bg-info';
                if (log.operationType === 'CREATE') {
                    operationBadgeClass = 'bg-primary';
                } else if (log.operationType === 'UPDATE') {
                    operationBadgeClass = 'bg-info';
                } else if (log.operationType === 'DELETE') {
                    operationBadgeClass = 'bg-danger';
                } else if (log.operationType === 'LOGIN') {
                    operationBadgeClass = 'bg-success';
                } else if (log.operationType === 'LOGOUT') {
                    operationBadgeClass = 'bg-secondary';
                }
                
                // 格式化详情信息，移除ISO格式的日期时间
                let details = log.details || '无详细信息';
                // 替换ISO格式的日期时间为更友好的格式
                details = details.replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(:\d{2})?/g, function(match) {
                    try {
                        const dt = new Date(match);
                        if (!isNaN(dt.getTime())) {
                            return dt.toLocaleString('zh-CN', {
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: false
                            });
                        }
                        return match;
                    } catch (e) {
                        return match;
                    }
                });
                
                html += `
                    <li class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            ${formattedTime ? `<small class="text-muted">${formattedTime}</small>` : ''}
                            <div>
                                <span class="badge ${operationBadgeClass}">${log.operationType || '未知操作'}</span>
                                <span class="badge ${statusBadgeClass}">${log.status || '未知状态'}</span>
                            </div>
                        </div>
                        <div class="mt-1">
                            <strong>${log.username || '未知用户'}</strong>: 
                            ${details}
                        </div>
                    </li>
                `;
            });
            
            recentLogsElement.innerHTML = html;
        }
        
        // 初始化页面
        $(document).ready(function() {
            // 初始化日历
            initCalendar();
            
            // 加载最近的审计日志
            loadRecentAuditLogs();
            
            // 视图切换按钮点击事件
            $('.btn-group [data-view]').on('click', function() {
                const viewName = $(this).data('view');
                // 移除所有按钮的active类
                $('.btn-group [data-view]').removeClass('active');
                // 添加当前按钮的active类
                $(this).addClass('active');
                // 切换日历视图
                calendar.changeView(viewName);
            });
            
            // 默认选中周视图按钮
            $('.btn-group [data-view="timeGridWeek"]').addClass('active');
            
            // 加载会议室列表
            loadMeetingRooms();
            
            // 日期选择器前往按钮点击事件
            $('#go-to-date').on('click', function() {
                const selectedDate = $('#date-picker').val();
                if (selectedDate) {
                    // 跳转到所选日期
                    calendar.gotoDate(selectedDate);
                }
            });
            
            // 设置日期选择器默认值为今天
            const today = new Date();
            const formattedDate = today.toISOString().split('T')[0];
            $('#date-picker').val(formattedDate);
        });
        
        // 加载会议室列表到仪表盘过滤器
        function loadMeetingRooms() {
            $.ajax({
                url: '/meeting-rooms/active',
                method: 'GET',
                success: function(data) {
                    // 清空会议室列表，保留"所有会议室"选项
                    const allRoomsItem = $('#room-filter li:first-child');
                    $('#room-filter').empty().append(allRoomsItem);
                    
                    // 检查数据结构
                    let rooms = data;
                    if (data.data) {
                        rooms = data.data;
                    }
                    
                    // 添加会议室到过滤器列表
                    if (rooms && rooms.length > 0) {
                        // 更新"所有会议室"的计数
                        allRoomsItem.find('.badge').text(rooms.length);
                        
                        rooms.forEach(function(room) {
                            $('#room-filter').append(`
                                <li class="list-group-item room-filter-item" data-room-id="${room.id}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>${room.name}</span>
                                    </div>
                                </li>
                            `);
                        });
                        
                        // 绑定会议室过滤器点击事件
                        $('.room-filter-item').on('click', function() {
                            const roomId = $(this).data('room-id');
                            
                            // 移除所有项目的active类
                            $('.room-filter-item').removeClass('active');
                            // 添加当前项目的active类
                            $(this).addClass('active');
                            
                            // 加载该会议室的预约或所有预约
                            if (roomId === 'all') {
                                loadRoomReservations(null); // 加载所有会议室的预约
                            } else {
                                loadRoomReservations(roomId); // 加载特定会议室的预约
                            }
                        });
                    } else {
                        // 如果没有会议室，更新"所有会议室"的计数为0
                        allRoomsItem.find('.badge').text('0');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载会议室失败:', error);
                    // 保留"所有会议室"选项，但计数为0
                    $('#room-filter li:first-child').find('.badge').text('0');
                    $('#room-filter').append('<li class="list-group-item text-danger">加载会议室失败</li>');
                }
            });
        }
        
        // 加载指定会议室的预约
        function loadRoomReservations(roomId) {
            if (calendar) {
                // 更新事件源
                calendar.getEventSources().forEach(source => source.remove());
                
                // 构建URL，根据是否有roomId参数
                let url = '/meeting/reservations/events';
                if (roomId) {
                    url += `?roomId=${roomId}`;
                }
                
                calendar.addEventSource({
                    url: url,
                    method: 'GET',
                    failure: function() {
                        alert('加载预约数据失败');
                    }
                });
            }
        }
    </script>
    
    <!-- 引入预约表单脚本 -->
    <!--<div th:replace="~{fragments/reservation-form-modal :: reservationFormScripts}"></div>-->
</body>
</html>
