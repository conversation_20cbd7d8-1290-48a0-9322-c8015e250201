<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" 
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于系统 - 会议室预约系统</title>
</head>
<body>
    <!-- 使用默认布局 -->
    <th:block th:replace="~{layout/default :: html(
        content=~{::content},
        pageTitle='关于系统'
    )}">
        <th:block th:fragment="content">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">关于会议室预约系统</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>系统介绍</h5>
                            <p>会议室预约系统是一个专为企业内部设计的会议室资源管理平台，旨在提高会议室使用效率，减少资源冲突，为企业员工提供便捷的会议室预约服务。</p>
                            
                            <h5 class="mt-4">主要功能</h5>
                            <ul>
                                <li><strong>会议室管理：</strong> 管理员可以添加、编辑、删除会议室，设置会议室属性如名称、位置、容量、设备等。</li>
                                <li><strong>会议室预约：</strong> 用户可以查看会议室状态，选择空闲时段进行预约，填写会议主题、参会人数等信息。</li>
                                <li><strong>预约管理：</strong> 用户可以查看、修改、取消自己的预约记录。</li>
                                <li><strong>日历视图：</strong> 直观展示各会议室的预约情况，支持日、周、月多种视图模式。</li>
                                <li><strong>用户管理：</strong> 管理员可以管理系统用户，分配不同的角色和权限。</li>
                            </ul>
                            
                            <h5 class="mt-4">技术架构</h5>
                            <p>本系统采用前后端不分离的架构，基于以下技术栈开发：</p>
                            <ul>
                                <li><strong>后端：</strong> Spring Boot 3, Spring Security, MyBatis</li>
                                <li><strong>前端：</strong> Thymeleaf, Bootstrap 5, FullCalendar</li>
                                <li><strong>数据库：</strong> MySQL</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">系统信息</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>系统版本</span>
                                            <span class="badge bg-primary">1.0.0</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>发布日期</span>
                                            <span>2023-05-16</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>开发团队</span>
                                            <span>企业内部开发团队</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="card mt-4">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">联系我们</h5>
                                </div>
                                <div class="card-body">
                                    <p><i class="bi bi-envelope"></i> 邮箱：<EMAIL></p>
                                    <p><i class="bi bi-telephone"></i> 电话：(+86) 123-4567-8910</p>
                                    <p><i class="bi bi-geo-alt"></i> 地址：北京市朝阳区科技园区</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">使用指南</h5>
                                </div>
                                <div class="card-body">
                                    <div class="accordion" id="accordionGuide">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingOne">
                                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                                    如何预约会议室？
                                                </button>
                                            </h2>
                                            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionGuide">
                                                <div class="accordion-body">
                                                    <ol>
                                                        <li>登录系统后，点击导航栏中的"预约会议室"或仪表盘中的"新建预约"按钮</li>
                                                        <li>在预约表单中，选择会议室、日期和时间段</li>
                                                        <li>填写会议主题、申请人姓名、联系方式、参会人数等信息</li>
                                                        <li>点击"提交"按钮完成预约</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingTwo">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                                    如何查看和管理我的预约？
                                                </button>
                                            </h2>
                                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionGuide">
                                                <div class="accordion-body">
                                                    <ol>
                                                        <li>登录系统后，点击导航栏中的"我的预约"</li>
                                                        <li>在列表中可以查看所有您创建的预约记录</li>
                                                        <li>点击"编辑"按钮可以修改预约信息</li>
                                                        <li>点击"取消"按钮可以取消预约</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingThree">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                                    如何修改个人信息？
                                                </button>
                                            </h2>
                                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionGuide">
                                                <div class="accordion-body">
                                                    <ol>
                                                        <li>点击导航栏右上角的用户名，在下拉菜单中选择"个人资料"</li>
                                                        <li>在个人资料页面，点击"编辑资料"按钮</li>
                                                        <li>修改您的个人信息，如姓名、邮箱、部门等</li>
                                                        <li>点击"保存"按钮完成修改</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">会议室预约系统</p>
                </div>
            </div>
        </th:block>
    </th:block>
</body>
</html>