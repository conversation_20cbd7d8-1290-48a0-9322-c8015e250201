# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /meeting
    session:
      timeout: 30m

# DataSource Configuration
spring:
  datasource:
    url: *************************************************************************************************************************************
    username: root
    password: Admin@123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      # 开启预编译语句
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: true
      useServerPrepStmts: true
  
  # Thymeleaf Configuration
  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    suffix: .html
    encoding: UTF-8
    mode: HTML
  
  # Static Resources
  web:
    resources:
      static-locations: classpath:/static/
  
  # Mail Configuration
  mail:
    host: smtp.example.com
    port: 587
    username: <EMAIL>
    password: your-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      
  # Disable all actuator endpoints
  boot:
    admin:
      client:
        enabled: false
  
  # <PERSON>配置，防止XSS攻击
  jackson:
    deserialization:
      fail-on-unknown-properties: true
    mapper:
      accept-case-insensitive-properties: true
  
# MyBatis Configuration
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.meetingroomreservation.entity
  configuration:
    map-underscore-to-camel-case: true

# Logging Configuration
logging:
  level:
    com:
      example:
        meetingroomreservation: DEBUG
        meetingroomreservation.mapper: DEBUG
    org:
      springframework:
        web: INFO
      mybatis: INFO

# Actuator Configuration (Disabled)
management:
  endpoints:
    web:
      exposure:
        include: none
  endpoint:
    health:
      enabled: false
    info:
      enabled: false
    metrics:
      enabled: false
    env:
      enabled: false
    beans:
      enabled: false
    configprops:
      enabled: false
    mappings:
      enabled: false
    threaddump:
      enabled: false
    heapdump:
      enabled: false
    loggers:
      enabled: false
    auditevents:
      enabled: false
    scheduledtasks:
      enabled: false
    httptrace:
      enabled: false
    prometheus:
      enabled: false
  health:
    defaults:
      enabled: false

# 安全配置
security:
  # 防XSS攻击配置
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  headers:
    xss-protection: 1; mode=block
    content-type-options: nosniff
    frame-options: DENY
    
  # CSRF保护配置
  csrf:
    cookie:
      http-only: false
      secure: true
