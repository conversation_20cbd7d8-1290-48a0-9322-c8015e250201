<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>API端点测试页面</h1>
    
    <div class="test-section">
        <h3>测试1: 基本API端点</h3>
        <button onclick="testBasicApi()">测试 /api/test</button>
        <div id="test1-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 获取RSA公钥</h3>
        <button onclick="testPublicKey()">测试 /api/public-key</button>
        <div id="test2-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 加密登录API</h3>
        <button onclick="testEncryptedLogin()">测试 /api/encrypted-login</button>
        <div id="test3-result" class="result"></div>
    </div>

    <script>
        async function testBasicApi() {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch('/api/test');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }
        
        async function testPublicKey() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch('/api/public-key');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }
        
        async function testEncryptedLogin() {
            const resultDiv = document.getElementById('test3-result');
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch('/api/encrypted-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: 'test'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 200) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = `状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
