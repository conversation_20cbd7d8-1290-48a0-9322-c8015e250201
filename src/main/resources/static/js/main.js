/**
 * 会议室预约系统主要JavaScript文件
 * 包含通用函数和初始化代码
 */

// 文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('会议室预约系统初始化完成');
    
    // 初始化Bootstrap提示工具
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化Bootstrap弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 添加表单验证
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // 通用的AJAX错误处理
    $(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
        console.error('AJAX请求失败:', thrownError);
        if (jqXHR.status === 401) {
            // 未授权，重定向到登录页面
            window.location.href = '/meeting/login';
        } else if (jqXHR.status === 403) {
            // 禁止访问
            alert('您没有权限执行此操作');
        } else if (jqXHR.status === 404) {
            // 资源未找到
            console.error('请求的资源不存在:', ajaxSettings.url);
        } else if (jqXHR.status >= 500) {
            // 服务器错误
            console.error('服务器错误:', jqXHR.responseText);
        }
    });
});

/**
 * 格式化日期时间
 * @param {Date|string} date - 日期对象或日期字符串
 * @param {string} format - 格式化模式，默认为'YYYY-MM-DD HH:mm'
 * @returns {string} 格式化后的日期字符串
 */
function formatDateTime(date, format = 'YYYY-MM-DD HH:mm') {
    if (!date) return '';
    
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型：success, info, warning, danger
 * @param {number} duration - 显示时长（毫秒），默认3000ms
 */
function showNotification(message, type = 'info', duration = 3000) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `toast align-items-center text-white bg-${type} border-0`;
    notification.setAttribute('role', 'alert');
    notification.setAttribute('aria-live', 'assertive');
    notification.setAttribute('aria-atomic', 'true');
    
    // 设置通知内容
    notification.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    // 添加到通知容器
    const container = document.querySelector('.toast-container');
    if (!container) {
        // 如果不存在通知容器，创建一个
        const newContainer = document.createElement('div');
        newContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(newContainer);
        newContainer.appendChild(notification);
    } else {
        container.appendChild(notification);
    }
    
    // 显示通知
    const toast = new bootstrap.Toast(notification, {
        autohide: true,
        delay: duration
    });
    toast.show();
    
    // 通知关闭后移除元素
    notification.addEventListener('hidden.bs.toast', function() {
        notification.remove();
    });
}
