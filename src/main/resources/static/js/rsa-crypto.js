/**
 * 前端RSA加密工具类
 */
class RSAEncrypt {
    constructor() {
        this.publicKey = null;
        this.encrypt = null;
    }

    async getPublicKey() {
        if (this.publicKey) {
            return this.publicKey;
        }

        try {
            const response = await fetch('/meeting/api/public-key');
            const data = await response.json();
            this.publicKey = data.publicKey;

            if (typeof JSEncrypt !== 'undefined') {
                this.encrypt = new JSEncrypt();
                this.encrypt.setPublicKey(this.publicKey);
            }

            return this.publicKey;
        } catch (error) {
            console.error('获取RSA公钥失败:', error);
            throw error;
        }
    }

    async encryptPassword(password) {
        await this.getPublicKey();

        if (this.encrypt) {
            const encrypted = this.encrypt.encrypt(password);
            if (!encrypted) {
                throw new Error('RSA加密失败');
            }
            return encrypted;
        } else {
            throw new Error('JSEncrypt库未加载，无法进行RSA加密');
        }
    }
}

const rsaEncrypt = new RSAEncrypt();

async function handleEncryptedLogin(event) {
    event.preventDefault();

    const form = event.target;
    const username = form.username.value.trim();
    const password = form.password.value;

    if (!username || !password) {
        showMessage('请输入用户名和密码', 'error');
        return;
    }

    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // 防止重复提交
    if (submitBtn.disabled) {
        return;
    }

    try {
        // 设置加载状态
        submitBtn.textContent = '登录中...';
        submitBtn.disabled = true;
        submitBtn.classList.add('loading');

        const encryptedPassword = await rsaEncrypt.encryptPassword(password);

        const response = await fetch('/api/encrypted-login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: encryptedPassword
            })
        });

        const result = await response.json();

        if (result.success) {
            submitBtn.textContent = '登录成功';
            submitBtn.classList.remove('loading');
            submitBtn.classList.add('success');
            showMessage(result.message, 'success');

            setTimeout(() => {
                window.location.href = result.redirectUrl || '/dashboard';
            }, 1000);
        } else {
            // 登录失败时恢复按钮状态
            resetButtonState(submitBtn, originalText);
            showMessage(result.message, 'error');
        }

    } catch (error) {
        // 发生错误时恢复按钮状态
        resetButtonState(submitBtn, originalText);
        console.error('登录失败:', error);
        showMessage('登录失败：' + error.message, 'error');
    }
}

function resetButtonState(button, originalText) {
    button.textContent = originalText;
    button.disabled = false;
    button.classList.remove('loading', 'success');
}

function showMessage(message, type = 'info') {
    const existingAlert = document.querySelector('.alert-message');
    if (existingAlert) {
        existingAlert.remove();
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show alert-message`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const form = document.querySelector('#loginForm');
    if (form) {
        form.parentNode.insertBefore(alertDiv, form);
    }

    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.querySelector('#loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleEncryptedLogin);
    }

    rsaEncrypt.getPublicKey().catch(error => {
        console.error('预加载公钥失败:', error);
    });
});
