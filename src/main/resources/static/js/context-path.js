/**
 * Context Path 工具类
 * 用于获取应用程序的context-path
 */
class ContextPath {
    constructor() {
        this.contextPath = this.getContextPath();
    }

    /**
     * 从当前页面URL获取context-path
     * @returns {string} context-path
     */
    getContextPath() {
        const path = window.location.pathname;
        const segments = path.split('/');
        
        // 如果URL是 /meeting/login 这样的格式，返回 /meeting
        // 如果URL是 /login 这样的格式，返回空字符串
        if (segments.length > 1 && segments[1]) {
            return '/' + segments[1];
        }
        return '';
    }

    /**
     * 构建完整的API URL
     * @param {string} apiPath - API路径，如 '/api/public-key'
     * @returns {string} 完整的URL
     */
    buildApiUrl(apiPath) {
        return this.contextPath + apiPath;
    }

    /**
     * 构建完整的页面URL
     * @param {string} pagePath - 页面路径，如 '/login'
     * @returns {string} 完整的URL
     */
    buildPageUrl(pagePath) {
        return this.contextPath + pagePath;
    }
}

// 创建全局实例
window.contextPath = new ContextPath();
