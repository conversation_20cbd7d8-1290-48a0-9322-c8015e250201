package com.example.meetingroomreservation.controller;

import com.example.meetingroomreservation.util.RSAEncryptUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * Authentication Controller
 */
@Controller
public class AuthController {

    private final AuthenticationManager authenticationManager;

    @Autowired
    public AuthController(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
    }

    /**
     * 获取RSA公钥API
     * Get RSA public key API
     */
    @GetMapping("/api/public-key")
    @ResponseBody
    public ResponseEntity<Map<String, String>> getPublicKey() {
        Map<String, String> response = new HashMap<>();
        response.put("publicKey", RSAEncryptUtil.getClientPublicKey());
        response.put("status", "success");
        return ResponseEntity.ok(response);
    }

    /**
     * 测试API端点是否可访问
     * Test API endpoint accessibility
     */
    @GetMapping("/api/test")
    @ResponseBody
    public ResponseEntity<Map<String, String>> testApi() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "API端点可以正常访问");
        response.put("status", "success");
        return ResponseEntity.ok(response);
    }

    /**
     * 处理RSA加密登录请求
     * Handle RSA encrypted login request
     */
    @PostMapping("/api/encrypted-login")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> encryptedLogin(
            @RequestBody Map<String, String> loginRequest,
            HttpServletRequest request) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 获取加密的用户名和密码
            String username = loginRequest.get("username");
            String encryptedPassword = loginRequest.get("password");

            // 调试信息
            response.put("debug", "API端点可以访问");
            response.put("receivedUsername", username);
            response.put("receivedPasswordLength", encryptedPassword != null ? encryptedPassword.length() : 0);

            // 如果没有加密密码，直接返回错误
            if (encryptedPassword == null || encryptedPassword.isEmpty()) {
                response.put("success", false);
                response.put("message", "密码不能为空");
                return ResponseEntity.ok(response);
            }

            // 使用RSA私钥解密密码
            String password;
            try {
                password = RSAEncryptUtil.decrypt(encryptedPassword);
            } catch (Exception decryptError) {
                response.put("success", false);
                response.put("message", "密码解密失败: " + decryptError.getMessage());
                return ResponseEntity.ok(response);
            }

            // 创建认证令牌
            UsernamePasswordAuthenticationToken authToken =
                new UsernamePasswordAuthenticationToken(username, password);

            // 设置请求详情
            authToken.setDetails(new WebAuthenticationDetails(request));

            // 执行认证
            Authentication authentication = authenticationManager.authenticate(authToken);

            // 设置安全上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 创建会话
            HttpSession session = request.getSession(true);
            session.setAttribute("SPRING_SECURITY_CONTEXT", SecurityContextHolder.getContext());

            response.put("success", true);
            response.put("message", "登录成功");
            response.put("redirectUrl", "/dashboard");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "认证失败: " + e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
        }

        return ResponseEntity.ok(response);
    }
}
